flutter: #152    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #153    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #154    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #155    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #156    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #157    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #158    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #159    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #160    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #161    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #162    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #163    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #164    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #165    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #166    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #167    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #168    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #169    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #170    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #171    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #172    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #173    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #174    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #175    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #176    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #177    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #178    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #179    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #180    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #181    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #182    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #183    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #184    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #185    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #186    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #187    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #188    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #189    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #190    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #191    _invoke (dart:ui/hooks.dart:312:13)
flutter: #192    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #193    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-08-02T01:02:12.492615] [FlutterError] A RenderFlex overflowed by 50 pixels on the bottom. A RenderFlex overflowed by 50 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderTransform.paint (package:flutter/src/rendering/proxy_box.dart:2583:17)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #29     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #30     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #39     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #40     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #41     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #42     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:750:7)
flutter: #43     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #44     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #45     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #46     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #47     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #48     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #49     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #50     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #51     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #52     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #53     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #54     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #55     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #56     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #57     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #58     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #59     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #60     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #61     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #62     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #63     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #64     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #65     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #66     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #67     RenderOpacity.paint (package:flutter/src/rendering/proxy_box.dart:970:11)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #70     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #71     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #72     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #73     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #74     RenderTransform.paint (package:flutter/src/rendering/proxy_box.dart:2583:17)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #78     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #79     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #80     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #81     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #85     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #86     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #87     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #88     _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #89     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #90     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #91     _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #92     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #93     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #94     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #95     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #96     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #97     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #104    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #105    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #106    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #107    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #108    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #109    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #110    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #111    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #112    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #113    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #114    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #115    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #116    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #117    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #118    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #119    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #120    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #121    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #122    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #123    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #124    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #125    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #130    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #131    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #138    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #142    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #143    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #144    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #145    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #146    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #147    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #148    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #149    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #150    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #151    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #152    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #153    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #154    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #155    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #156    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #157    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #158    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #159    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #160    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #161    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #162    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #163    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #164    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #165    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #166    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #167    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #168    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #169    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #170    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #171    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #172    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #173    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #174    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #175    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #176    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #177    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #178    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #179    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #180    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #181    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #182    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #183    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #184    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #185    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #186    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #187    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #188    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #189    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #190    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #191    _invoke (dart:ui/hooks.dart:312:13)
flutter: #192    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #193    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-08-02T01:02:12.496662] [FlutterError] A RenderFlex overflowed by 27 pixels on the bottom. A RenderFlex overflowed by 27 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderTransform.paint (package:flutter/src/rendering/proxy_box.dart:2583:17)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #29     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #30     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #39     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #40     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #41     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #42     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:750:7)
flutter: #43     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #44     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #45     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #46     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #47     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #48     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #49     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #50     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #51     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #52     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #53     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #54     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #55     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #56     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #57     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #58     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #59     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #60     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #61     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #62     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #63     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #64     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #65     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #66     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #67     RenderOpacity.paint (package:flutter/src/rendering/proxy_box.dart:970:11)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #70     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #71     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #72     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #73     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #74     RenderTransform.paint (package:flutter/src/rendering/proxy_box.dart:2583:17)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #78     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #79     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #80     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #81     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #85     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #86     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #87     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #88     _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #89     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #90     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #91     _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #92     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #93     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #94     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #95     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #96     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #97     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #104    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #105    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #106    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #107    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #108    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #109    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #110    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #111    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #112    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #113    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #114    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #115    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #116    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #117    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #118    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #119    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #120    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #121    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #122    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #123    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #124    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #125    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #130    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #131    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #138    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #142    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #143    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #144    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #145    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #146    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #147    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #148    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #149    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #150    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #151    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #152    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #153    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #154    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #155    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #156    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #157    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #158    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #159    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #160    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #161    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #162    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #163    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #164    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #165    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #166    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #167    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #168    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #169    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #170    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #171    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #172    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #173    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #174    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #175    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #176    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #177    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #178    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #179    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #180    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #181    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #182    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #183    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #184    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #185    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #186    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #187    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #188    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #189    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #190    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #191    _invoke (dart:ui/hooks.dart:312:13)
flutter: #192    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #193    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-08-02T01:02:12.500954] [FlutterError] A RenderFlex overflowed by 27 pixels on the bottom. A RenderFlex overflowed by 27 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderTransform.paint (package:flutter/src/rendering/proxy_box.dart:2583:17)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #29     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #30     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #39     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #40     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #41     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #42     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:750:7)
flutter: #43     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #44     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #45     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #46     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #47     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #48     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #49     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #50     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #51     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #52     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #53     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #54     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #55     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #56     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #57     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #58     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #59     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #60     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #61     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #62     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #63     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #64     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #65     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #66     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #67     RenderOpacity.paint (package:flutter/src/rendering/proxy_box.dart:970:11)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #70     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #71     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #72     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #73     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #74     RenderTransform.paint (package:flutter/src/rendering/proxy_box.dart:2583:17)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #78     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #79     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #80     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #81     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #85     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #86     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #87     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #88     _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #89     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #90     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #91     _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #92     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #93     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #94     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #95     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #96     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #97     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #104    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #105    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #106    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #107    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #108    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #109    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #110    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #111    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #112    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #113    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #114    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #115    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #116    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #117    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #118    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #119    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #120    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #121    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #122    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #123    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #124    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #125    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #130    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #131    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #138    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #142    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #143    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #144    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #145    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #146    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #147    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #148    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #149    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #150    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #151    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #152    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #153    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #154    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #155    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #156    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #157    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #158    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #159    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #160    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #161    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #162    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #163    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #164    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #165    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #166    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #167    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #168    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #169    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #170    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #171    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #172    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #173    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #174    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #175    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #176    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #177    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #178    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #179    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #180    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #181    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #182    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #183    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #184    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #185    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #186    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #187    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #188    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #189    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #190    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #191    _invoke (dart:ui/hooks.dart:312:13)
flutter: #192    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #193    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-08-02T01:02:12.696096] [PerformanceMonitoringService] Slow frame detected {"duration_ms":916}
flutter: 🐛 DEBUG [2025-08-02T01:02:12.728328] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-02T01:02:12.779525] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-02T01:02:12.828782] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-08-02T01:02:13.583726] [PerformanceMonitoringService] Slow frame detected {"duration_ms":105}
flutter: 🐛 DEBUG [2025-08-02T01:02:13.611582] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-08-02T01:02:14.700594] [PerformanceMonitoringService] Slow frame detected {"duration_ms":122}
flutter: 🐛 DEBUG [2025-08-02T01:02:14.728870] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-08-02T01:02:15.800293] [PerformanceMonitoringService] Slow frame detected {"duration_ms":105}
flutter: 🐛 DEBUG [2025-08-02T01:02:15.828359] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-08-02T01:02:16.582955] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-08-02T01:02:16.611537] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-08-02T01:02:16.765007] [PerformanceMonitoringService] Slow frame detected {"duration_ms":119}
flutter: 🐛 DEBUG [2025-08-02T01:02:17.009805] [PerformanceMonitoringService] Slow frame detected {"duration_ms":243}
flutter: 🐛 DEBUG [2025-08-02T01:02:17.128010] [PerformanceMonitoringService] Slow frame detected {"duration_ms":120}
flutter: 🐛 DEBUG [2025-08-02T01:02:17.246274] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-08-02T01:02:17.350765] [PerformanceMonitoringService] Slow frame detected {"duration_ms":106}
flutter: 🐛 DEBUG [2025-08-02T01:02:17.432352] [PerformanceMonitoringService] Slow frame detected {"duration_ms":77}
flutter: 🐛 DEBUG [2025-08-02T01:02:17.477410] [PerformanceMonitoringService] Slow frame detected {"duration_ms":48}
flutter: 🐛 DEBUG [2025-08-02T01:02:17.561612] [PerformanceMonitoringService] Slow frame detected {"duration_ms":85}
flutter: 🐛 DEBUG [2025-08-02T01:02:17.604642] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-08-02T01:02:17.678596] [PerformanceMonitoringService] Slow frame detected {"duration_ms":73}
flutter: 🐛 DEBUG [2025-08-02T01:02:17.764025] [PerformanceMonitoringService] Slow frame detected {"duration_ms":85}
flutter: 🐛 DEBUG [2025-08-02T01:02:17.895911] [PerformanceMonitoringService] Slow frame detected {"duration_ms":130}
flutter: 🐛 DEBUG [2025-08-02T01:02:17.961262] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:18.201140] [PerformanceMonitoringService] Slow frame detected {"duration_ms":223}
flutter: 🐛 DEBUG [2025-08-02T01:02:18.245471] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-08-02T01:02:18.344979] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-08-02T01:02:18.381219] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-08-02T01:02:18.496951] [PerformanceMonitoringService] Slow frame detected {"duration_ms":113}
flutter: 🐛 DEBUG [2025-08-02T01:02:18.596009] [PerformanceMonitoringService] Slow frame detected {"duration_ms":101}
flutter: 🐛 DEBUG [2025-08-02T01:02:18.629463] [PerformanceMonitoringService] Slow frame detected {"duration_ms":31}
flutter: 🐛 DEBUG [2025-08-02T01:02:18.694926] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-02T01:02:18.761465] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:18.839928] [PerformanceMonitoringService] Slow frame detected {"duration_ms":78}
flutter: 🐛 DEBUG [2025-08-02T01:02:18.928340] [PerformanceMonitoringService] Slow frame detected {"duration_ms":88}
flutter: 🐛 DEBUG [2025-08-02T01:02:18.995109] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.053579] [PerformanceMonitoringService] Slow frame detected {"duration_ms":58}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.145781] [PerformanceMonitoringService] Slow frame detected {"duration_ms":91}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.209583] [PerformanceMonitoringService] Slow frame detected {"duration_ms":64}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.278994] [PerformanceMonitoringService] Slow frame detected {"duration_ms":68}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.362525] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.428543] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: ⚠️ WARNING [2025-08-02T01:02:19.491] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.495475] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.562413] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.595172] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.662158] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.695417] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.795748] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.861783] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.911543] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-08-02T01:02:19.978493] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.062462] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.128645] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.214638] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.278732] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.328426] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.420609] [PerformanceMonitoringService] Slow frame detected {"duration_ms":92}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.444847] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.495081] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.573306] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.654205] [PerformanceMonitoringService] Slow frame detected {"duration_ms":92}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.728369] [PerformanceMonitoringService] Slow frame detected {"duration_ms":73}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.784813] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.862156] [PerformanceMonitoringService] Slow frame detected {"duration_ms":76}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.897921] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.934467] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-08-02T01:02:20.995350] [PerformanceMonitoringService] Slow frame detected {"duration_ms":59}
flutter: 🐛 DEBUG [2025-08-02T01:02:21.061478] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:21.128743] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:21.179431] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-08-02T01:02:21.245102] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:21.295102] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-08-02T01:02:21.362382] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:21.394915] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-02T01:02:21.461608] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-02T01:02:21.544924] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-08-02T01:02:21.594761] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-08-02T01:02:21.645481] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-02T01:02:21.728737] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}