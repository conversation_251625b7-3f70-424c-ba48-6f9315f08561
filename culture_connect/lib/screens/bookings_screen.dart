import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/services/booking_service.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/empty_state.dart';
import 'package:culture_connect/widgets/rn_components/rn_booking_card.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Provider for booking service
final bookingServiceProvider =
    Provider<BookingService>((ref) => BookingService());

/// Provider for user bookings
final userBookingsProvider = FutureProvider<List<Booking>>((ref) async {
  final bookingService = ref.read(bookingServiceProvider);
  return await bookingService.getUserBookings();
});

class BookingsScreen extends ConsumerWidget {
  const BookingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bookingsAsync = ref.watch(userBookingsProvider);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppTheme.backgroundSecondary,
      appBar: CustomAppBar(
        title: 'My Bookings',
        showBackButton: false,
        actions: [
          IconButton(
            icon: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: const [
                  BoxShadow(
                    color: AppTheme.shadowColor,
                    offset: Offset(0, 2),
                    blurRadius: 4,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: const Icon(
                Icons.refresh,
                color: AppTheme.textPrimaryColor,
                size: 20,
              ),
            ),
            onPressed: () => ref.refresh(userBookingsProvider),
            tooltip: 'Refresh bookings',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(userBookingsProvider);
          // Add a small delay for better UX
          await Future.delayed(const Duration(milliseconds: 500));
        },
        color: AppTheme.primaryColor,
        backgroundColor: AppTheme.white,
        child: bookingsAsync.when(
          loading: () => const Center(child: LoadingIndicator()),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load bookings',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: theme.colorScheme.error,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  error.toString(),
                  style: theme.textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.refresh(userBookingsProvider),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
          data: (bookings) => bookings.isEmpty
              ? const EmptyState(
                  title: 'No Bookings Yet',
                  message:
                      'Your bookings will appear here once you make your first reservation.',
                  icon: Icons.event_note,
                  actionText: 'Explore Experiences',
                )
              : _buildBookingsList(context, bookings, ref),
        ),
      ),
    );
  }

  Widget _buildBookingsList(
      BuildContext context, List<Booking> bookings, WidgetRef ref) {
    // Group bookings by status
    final upcomingBookings = bookings
        .where((b) =>
            b.status == BookingStatus.confirmed &&
            b.date.isAfter(DateTime.now()))
        .toList();
    final pastBookings = bookings
        .where((b) =>
            b.status == BookingStatus.completed ||
            b.date.isBefore(DateTime.now()))
        .toList();
    final pendingBookings =
        bookings.where((b) => b.status == BookingStatus.pending).toList();

    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppTheme.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: const [
                BoxShadow(
                  color: AppTheme.shadowColor,
                  offset: Offset(0, 2),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: TabBar(
              labelColor: AppTheme.white,
              unselectedLabelColor: AppTheme.textSecondaryColor,
              indicator: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(12),
              ),
              indicatorPadding: const EdgeInsets.all(4),
              dividerColor: Colors.transparent,
              labelStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              tabs: [
                Tab(
                  text: 'Upcoming (${upcomingBookings.length})',
                  icon: const Icon(Icons.schedule, size: 18),
                ),
                Tab(
                  text: 'Pending (${pendingBookings.length})',
                  icon: const Icon(Icons.hourglass_empty, size: 18),
                ),
                Tab(
                  text: 'Past (${pastBookings.length})',
                  icon: const Icon(Icons.history, size: 18),
                ),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildBookingTab(context, upcomingBookings, 'upcoming'),
                _buildBookingTab(context, pendingBookings, 'pending'),
                _buildBookingTab(context, pastBookings, 'past'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingTab(
      BuildContext context, List<Booking> bookings, String type) {
    if (bookings.isEmpty) {
      String message;
      String actionText;
      switch (type) {
        case 'upcoming':
          message = 'No upcoming bookings. Book an experience to see it here.';
          actionText = 'Browse Experiences';
          break;
        case 'pending':
          message =
              'No pending bookings. Your booking requests will appear here.';
          actionText = 'Make a Booking';
          break;
        case 'past':
          message =
              'No past bookings yet. Your completed experiences will appear here.';
          actionText = 'Explore Now';
          break;
        default:
          message = 'No bookings found.';
          actionText = 'Get Started';
      }

      return EmptyState(
        title: 'No ${type.capitalize()} Bookings',
        message: message,
        icon: type == 'upcoming'
            ? Icons.event
            : type == 'pending'
                ? Icons.pending_actions
                : Icons.history,
        actionText: actionText,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: bookings.length,
      itemBuilder: (context, index) => AnimatedContainer(
        duration: Duration(milliseconds: 200 + (index * 50)),
        curve: Curves.easeOutCubic,
        child: RNBookingCard(
          booking: bookings[index],
          onTap: () => _handleBookingTap(context, bookings[index]),
          onCancelTap: bookings[index].status == BookingStatus.confirmed
              ? () => _handleCancelBooking(context, bookings[index])
              : null,
          onRescheduleTap: bookings[index].status == BookingStatus.confirmed
              ? () => _handleRescheduleBooking(context, bookings[index])
              : null,
        ),
      ),
    );
  }

  void _handleBookingTap(BuildContext context, Booking booking) {
    // Navigate to booking details
    // TODO: Implement navigation to booking details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing details for booking #${booking.experienceId}'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _handleCancelBooking(BuildContext context, Booking booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: Text(
            'Are you sure you want to cancel booking #${booking.experienceId}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Keep Booking'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement booking cancellation
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Booking cancellation requested'),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('Cancel Booking'),
          ),
        ],
      ),
    );
  }

  void _handleRescheduleBooking(BuildContext context, Booking booking) {
    // TODO: Implement booking rescheduling
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Rescheduling booking #${booking.experienceId}'),
        backgroundColor: AppTheme.secondaryColor,
      ),
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
