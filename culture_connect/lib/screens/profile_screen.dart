import 'dart:async';
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/models/profile_models.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/navigation_provider.dart';
import 'package:culture_connect/providers/preferences_provider.dart';
import 'package:culture_connect/screens/loyalty/loyalty_dashboard_screen.dart';
import 'package:culture_connect/screens/settings/security_settings_screen.dart';
import 'package:culture_connect/services/interactive_features_service.dart'
    as interactive;
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/custom_button.dart';
import 'package:culture_connect/widgets/interactive/enhanced_success_animations.dart';

// Animation constants for micro-interactions
class ProfileAnimations {
  static const Duration quickActionHover = Duration(milliseconds: 150);
  static const Duration cardPress = Duration(milliseconds: 200);
  static const Duration progressBar = Duration(milliseconds: 600);
  static const Duration achievementReveal = Duration(milliseconds: 400);
  static const Duration buttonFeedback = Duration(milliseconds: 100);
}

// Dark mode theme constants
class ProfileDarkTheme {
  static const Color cardBackground = Color(0xFF1E1E1E);
  static const Color cardBackgroundSecondary = Color(0xFF2A2A2A);
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFB3B3B3);
  static const Color textTertiary = Color(0xFF666666);
  static const Color iconPrimary = Color(0xFFFFFFFF);
  static const Color iconSecondary = Color(0xFFB3B3B3);
  static const Color shadowDark = Color(0xFF000000);
  static const Color borderDark = Color(0xFF333333);
}

// Accessibility constants for WCAG 2.1 AA compliance
class ProfileAccessibility {
  static const double minimumTouchTarget = 44.0; // Minimum 44x44 touch target
  static const double textScaleFactor = 1.0; // Support for text scaling
  static const Duration focusAnimationDuration = Duration(milliseconds: 200);

  // Semantic labels for screen readers
  static const String profileHeaderLabel = 'User profile information';
  static const String quickActionsLabel = 'Quick action buttons';
  static const String statisticsLabel = 'Travel statistics and progress';
  static const String achievementsLabel = 'User achievements and progress';
  static const String settingsLabel = 'Account and app settings';
}

// Performance optimization constants
class ProfilePerformance {
  static const int maxCachedWidgets =
      50; // Limit cached widgets to prevent memory leaks
  static const Duration cacheTimeout =
      Duration(minutes: 5); // Cache timeout for memory management
  static const int maxImageCacheSize = 100; // Maximum cached images
  static const double targetMemoryUsage = 100.0; // Target <100MB memory usage
  static const double targetFrameRate = 60.0; // Target 60fps

  // Widget keys for performance optimization
  static const Key profileHeaderKey = Key('profile_header');
  static const Key quickActionsKey = Key('quick_actions');
  static const Key statisticsKey = Key('statistics');
  static const Key achievementsKey = Key('achievements');
}

// Fallback data for offline/error scenarios
class ProfileFallbackData {
  static UserModel createFallbackUserModel(User firebaseUser) {
    final email = firebaseUser.email ?? '<EMAIL>';
    final displayName = firebaseUser.displayName ??
        email.split('@').first.replaceAll(RegExp(r'[^a-zA-Z]'), '');

    return UserModel(
      id: firebaseUser.uid,
      email: email,
      firstName: displayName.isNotEmpty ? displayName : 'Explorer',
      lastName: '',
      phoneNumber: firebaseUser.phoneNumber ?? '',
      userType: 'tourist',
      isVerified: firebaseUser.emailVerified,
      verificationLevel: firebaseUser.emailVerified ? 2 : 1,
      status: 'active',
      bio:
          'Welcome to CultureConnect! Complete your profile to get personalized travel recommendations.',
      profilePicture: firebaseUser.photoURL,
      languagePreferences: ['English'],
      culturalInterests: ['Food & Cuisine', 'History & Heritage'],
      createdAt: DateTime.now().toIso8601String(),
      updatedAt: DateTime.now().toIso8601String(),
      lastLogin: DateTime.now().toIso8601String(),
      emailVerified: firebaseUser.emailVerified,
    );
  }

  // Platform-specific error handling
  static bool isSimulatorEnvironment() {
    try {
      // Check if running on iOS simulator
      return Platform.isIOS &&
          !Platform.environment.containsKey('SIMULATOR_DEVICE_NAME') == false;
    } catch (e) {
      return false;
    }
  }

  static void handlePlatformSpecificErrors() {
    if (isSimulatorEnvironment()) {
      debugPrint(
          'Profile screen: Running on iOS simulator - enabling fallback mode');
      // Disable battery monitoring and other simulator-incompatible features
    }
  }

  static const List<TravelStatistic> mockTravelStats = [
    TravelStatistic(
      title: 'Countries Visited',
      value: '3',
      subtitle: 'Explore more destinations',
      icon: Icons.public,
      iconColor: AppTheme.primaryColor,
      progress: 0.3,
      progressColor: AppTheme.primaryColor,
    ),
    TravelStatistic(
      title: 'Cultural Experiences',
      value: '12',
      subtitle: 'Book your next adventure',
      icon: Icons.explore,
      iconColor: AppTheme.secondaryColor,
      progress: 0.6,
      progressColor: AppTheme.secondaryColor,
    ),
    TravelStatistic(
      title: 'Local Guides Met',
      value: '8',
      subtitle: 'Connect with more locals',
      icon: Icons.people,
      iconColor: Colors.green,
      progress: 0.4,
      progressColor: Colors.green,
    ),
    TravelStatistic(
      title: 'Photos Shared',
      value: '45',
      subtitle: 'Share your journey',
      icon: Icons.photo_camera,
      iconColor: Colors.orange,
      progress: 0.7,
      progressColor: Colors.orange,
    ),
  ];

  static const List<Achievement> mockAchievements = [
    Achievement(
      title: 'First Steps',
      description: 'Welcome to CultureConnect! Your journey begins here.',
      icon: Icons.star,
      gradientColors: [AppTheme.primaryColor, AppTheme.secondaryColor],
      unlocked: true,
      progress: 1.0,
    ),
    Achievement(
      title: 'Explorer',
      description: 'Visit 5 different countries',
      icon: Icons.explore,
      gradientColors: [Colors.blue, Colors.blueAccent],
      unlocked: false,
      progress: 0.6,
      progressText: '3/5 countries',
    ),
    Achievement(
      title: 'Culture Enthusiast',
      description: 'Complete 10 cultural experiences',
      icon: Icons.museum,
      gradientColors: [Colors.purple, Colors.purpleAccent],
      unlocked: false,
      progress: 0.8,
      progressText: '8/10 experiences',
    ),
  ];
}

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  bool _isEditing = false;
  bool _isLoading = false;
  File? _profileImageFile;
  final _formKey = GlobalKey<FormState>();

  // Controllers for editable fields
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _bioController = TextEditingController();
  final _phoneController = TextEditingController();

  // Selected preferences
  List<String> _selectedLanguages = [];
  List<String> _selectedInterests = [];

  // Animation controllers for micro-interactions
  late AnimationController _quickActionsController;
  late AnimationController _statisticsController;
  late AnimationController _achievementsController;
  late Animation<double> _quickActionsAnimation;
  late Animation<double> _statisticsAnimation;
  late Animation<double> _achievementsAnimation;

  // Performance optimization fields
  final Map<String, Widget> _widgetCache = <String, Widget>{};
  final Map<String, DateTime> _cacheTimestamps = <String, DateTime>{};
  final bool _isPerformanceOptimized = true;

  // Memory management
  Timer? _memoryCleanupTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Initialize animation controllers for micro-interactions
    _quickActionsController = AnimationController(
      duration: ProfileAnimations.quickActionHover,
      vsync: this,
    );
    _statisticsController = AnimationController(
      duration: ProfileAnimations.progressBar,
      vsync: this,
    );
    _achievementsController = AnimationController(
      duration: ProfileAnimations.achievementReveal,
      vsync: this,
    );

    // Initialize animations
    _quickActionsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _quickActionsController, curve: Curves.easeInOut),
    );
    _statisticsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _statisticsController, curve: Curves.easeInOut),
    );
    _achievementsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _achievementsController, curve: Curves.easeInOut),
    );

    _initializeProfile();

    // Initialize platform-specific error handling
    ProfileFallbackData.handlePlatformSpecificErrors();

    // Initialize performance optimization
    _initializePerformanceOptimization();

    // Start animations with staggered delays for smooth reveal
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) _quickActionsController.forward();
    });
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) _statisticsController.forward();
    });
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) _achievementsController.forward();
    });
  }

  // CRITICAL FIX: Refresh profile data when app resumes
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // Refresh profile data when app comes back to foreground
      _loadUserData();
    }
  }

  // CRITICAL FIX: Add comprehensive profile initialization without circular dependencies
  Future<void> _initializeProfile() async {
    debugPrint('Profile screen: Initializing profile...');

    // First check if we have a valid Firebase Auth user
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      debugPrint('Profile screen: No Firebase Auth user found');
      return;
    }

    debugPrint('Profile screen: Firebase Auth user found: ${currentUser.uid}');

    // Don't invalidate the provider here as it can cause infinite loops
    // The provider will handle data loading automatically

    await _loadUserData();
    await _checkAndInitializeProfileData();
  }

  // CRITICAL FIX: Check for missing profile data and initialize if needed
  Future<void> _checkAndInitializeProfileData() async {
    try {
      final userModel = await ref.read(currentUserModelProvider.future);
      if (userModel == null) return;

      // Check if profile data is incomplete
      bool needsUpdate = false;
      final Map<String, dynamic> updateData = {};

      // Initialize missing fields
      if (userModel.languagePreferences == null) {
        updateData['languagePreferences'] = <String>[];
        needsUpdate = true;
      }

      if (userModel.culturalInterests == null) {
        updateData['culturalInterests'] = <String>[];
        needsUpdate = true;
      }

      if (userModel.bio == null) {
        updateData['bio'] = null; // Explicitly set to null for consistency
        needsUpdate = true;
      }

      if (userModel.profilePicture == null) {
        updateData['profilePicture'] =
            null; // Explicitly set to null for consistency
        needsUpdate = true;
      }

      // Update Firestore if needed
      if (needsUpdate) {
        updateData['updatedAt'] = DateTime.now().toIso8601String();

        await FirebaseFirestore.instance
            .collection('users')
            .doc(userModel.id)
            .set(updateData, SetOptions(merge: true));

        // Refresh provider to get updated data
        ref.invalidate(currentUserModelProvider);

        debugPrint('Profile data initialized for user: ${userModel.id}');
      }
    } catch (e) {
      debugPrint('Error initializing profile data: $e');
      // Don't show error to user for initialization - it's a background process
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _firstNameController.dispose();
    _lastNameController.dispose();
    _bioController.dispose();
    _phoneController.dispose();

    // Dispose animation controllers
    _quickActionsController.dispose();
    _statisticsController.dispose();
    _achievementsController.dispose();

    // Dispose performance optimization resources
    _memoryCleanupTimer?.cancel();
    _clearWidgetCache();

    super.dispose();
  }

  // Performance optimization methods
  void _initializePerformanceOptimization() {
    // Start periodic memory cleanup to maintain <100MB target
    _memoryCleanupTimer =
        Timer.periodic(ProfilePerformance.cacheTimeout, (timer) {
      if (mounted) {
        _performMemoryCleanup();
        _monitorPerformance();
      }
    });
  }

  void _monitorPerformance() {
    // Monitor frame rate and memory usage
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final now = DateTime.now();
        final frameTime = now.difference(_lastFrameTime ?? now).inMilliseconds;

        // Log slow frames (>16ms for 60fps)
        if (frameTime > 16) {
          debugPrint('Profile screen: Slow frame detected: ${frameTime}ms');

          // If consistently slow, reduce animation complexity
          if (frameTime > 33) {
            // >30fps
            _reduceAnimationComplexity();
          }
        }

        _lastFrameTime = now;
      }
    });
  }

  DateTime? _lastFrameTime;

  void _reduceAnimationComplexity() {
    // Reduce animation complexity for better performance
    if (_quickActionsController.isAnimating) {
      _quickActionsController.stop();
    }
    if (_statisticsController.isAnimating) {
      _statisticsController.stop();
    }
    if (_achievementsController.isAnimating) {
      _achievementsController.stop();
    }

    debugPrint('Profile screen: Reduced animation complexity for performance');
  }

  void _performMemoryCleanup() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    // Find expired cache entries
    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > ProfilePerformance.cacheTimeout) {
        expiredKeys.add(entry.key);
      }
    }

    // Remove expired entries to prevent memory leaks
    for (final key in expiredKeys) {
      _widgetCache.remove(key);
      _cacheTimestamps.remove(key);
    }

    // Limit cache size to prevent excessive memory usage
    if (_widgetCache.length > ProfilePerformance.maxCachedWidgets) {
      final sortedEntries = _cacheTimestamps.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));

      final keysToRemove = sortedEntries
          .take(_widgetCache.length - ProfilePerformance.maxCachedWidgets)
          .map((e) => e.key)
          .toList();

      for (final key in keysToRemove) {
        _widgetCache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
  }

  void _clearWidgetCache() {
    _widgetCache.clear();
    _cacheTimestamps.clear();
  }

  Widget? _getCachedWidget(String key) {
    final cached = _widgetCache[key];
    final timestamp = _cacheTimestamps[key];

    if (cached != null && timestamp != null) {
      final now = DateTime.now();
      if (now.difference(timestamp) <= ProfilePerformance.cacheTimeout) {
        return cached;
      } else {
        // Remove expired cache entry
        _widgetCache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
    return null;
  }

  void _cacheWidget(String key, Widget widget) {
    if (_isPerformanceOptimized &&
        _widgetCache.length < ProfilePerformance.maxCachedWidgets) {
      _widgetCache[key] = widget;
      _cacheTimestamps[key] = DateTime.now();

      // Log cache usage for monitoring
      if (_widgetCache.length % 10 == 0) {
        debugPrint('Profile screen: Widget cache size: ${_widgetCache.length}');
      }
    }
  }

  Future<void> _loadUserData() async {
    debugPrint('Profile screen: _loadUserData called');
    try {
      // Check if we already have user data from the provider
      final currentState = ref.read(currentUserModelProvider);
      UserModel? userModel;

      if (currentState.hasValue && currentState.value != null) {
        debugPrint('Profile screen: Using cached user model');
        userModel = currentState.value;
      } else if (!currentState.isLoading) {
        debugPrint('Profile screen: Fetching user model from provider');
        userModel = await ref.read(currentUserModelProvider.future);
      } else {
        debugPrint('Profile screen: Provider is loading, skipping fetch');
        return;
      }

      if (userModel != null) {
        // Load user preferences from the preferences provider
        final prefs = ref.read(userPreferencesProvider);
        final user = userModel; // Create non-nullable reference

        setState(() {
          _firstNameController.text = user.firstName;
          _lastNameController.text = user.lastName;
          _bioController.text = user.bio ?? '';

          // CRITICAL FIX: Preserve full phone number with country code
          _phoneController.text = user.phoneNumber;

          // CRITICAL FIX: Prioritize Firestore data over local preferences for consistency
          _selectedLanguages = user.languagePreferences?.isNotEmpty == true
              ? user.languagePreferences!
              : prefs.languagePreferences;

          _selectedInterests = user.culturalInterests?.isNotEmpty == true
              ? user.culturalInterests!
              : prefs.culturalInterests;
        });

        // CRITICAL FIX: Sync preferences with Firestore data to ensure consistency
        if (userModel.languagePreferences?.isNotEmpty == true ||
            userModel.culturalInterests?.isNotEmpty == true) {
          await ref.read(userPreferencesProvider.notifier).syncWithUserProfile(
                culturalInterests: userModel.culturalInterests ?? [],
                languagePreferences: userModel.languagePreferences ?? [],
              );
        }
      } else {
        // CRITICAL FIX: Handle case where user model is null
        debugPrint(
            'Warning: User model is null, user may need to re-authenticate');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content:
                  Text('Unable to load profile data. Please try refreshing.'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      // CRITICAL FIX: Add comprehensive error handling
      debugPrint('Error loading user data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading profile: ${e.toString()}'),
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _loadUserData,
            ),
          ),
        );
      }
    }
  }

  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 800,
      maxHeight: 800,
      imageQuality: 85,
    );

    if (image != null) {
      setState(() {
        _profileImageFile = File(image.path);
      });
    }
  }

  Future<void> _pickImageWithFeedback() async {
    final interactiveService =
        ref.read(interactive.interactiveFeaturesServiceProvider);
    await interactiveService
        .triggerGestureFeedback(interactive.GestureType.longPress);
    await _pickImage();
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    // CRITICAL FIX: Add comprehensive validation
    final firstName = _firstNameController.text.trim();
    final lastName = _lastNameController.text.trim();
    final bio = _bioController.text.trim();
    final phoneNumber = _phoneController.text.trim();

    if (firstName.isEmpty || lastName.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('First name and last name are required'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userModel = await ref.read(currentUserModelProvider.future);
      if (userModel == null) throw Exception('User not authenticated');

      String? profilePictureUrl;

      // Upload profile picture if changed
      if (_profileImageFile != null) {
        final storageRef = FirebaseStorage.instance
            .ref()
            .child('profile_pictures')
            .child('${userModel.id}.jpg');

        await storageRef.putFile(_profileImageFile!);
        profilePictureUrl = await storageRef.getDownloadURL();
      }

      // CRITICAL FIX: Update user data with comprehensive field validation
      final userData = {
        'firstName': firstName,
        'lastName': lastName,
        'bio': bio.isNotEmpty ? bio : null,
        'phoneNumber':
            phoneNumber.isNotEmpty ? phoneNumber : userModel.phoneNumber,
        'languagePreferences': _selectedLanguages,
        'culturalInterests': _selectedInterests,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      if (profilePictureUrl != null) {
        userData['profilePicture'] = profilePictureUrl;
      }

      // CRITICAL FIX: Use merge option to avoid overwriting other fields
      await FirebaseFirestore.instance
          .collection('users')
          .doc(userModel.id)
          .set(userData, SetOptions(merge: true));

      // Refresh user data in provider
      ref.invalidate(currentUserModelProvider);

      // CRITICAL FIX: Always sync preferences with user profile for consistency
      await ref.read(userPreferencesProvider.notifier).syncWithUserProfile(
            culturalInterests: _selectedInterests,
            languagePreferences: _selectedLanguages,
          );

      setState(() {
        _isEditing = false;
        _isLoading = false;
        _profileImageFile = null; // Clear the selected image file
      });

      if (mounted) {
        // Show success animation instead of snackbar
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => EnhancedSuccessDialog(
            title: 'Profile Updated!',
            message: 'Your profile has been successfully updated.',
            successType: SuccessType.profile,
            confirmButtonText: 'Continue',
            onConfirm: () {
              // Dialog will auto-close
            },
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      debugPrint('Error saving profile: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating profile: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _saveProfile,
            ),
          ),
        );
      }
    }
  }

  Future<void> _signOut() async {
    try {
      await ref.read(authServiceProvider).signOut();
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/login');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error signing out: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final userModelAsync = ref.watch(currentUserModelProvider);
    final themeMode = ref.watch(themeModeProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    return PopScope(
      canPop: false, // Prevent back navigation that could cause logout
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // Navigate to explore tab instead of popping the route
          ref
              .read(navigationProvider.notifier)
              .setNavigationItem(NavigationItem.explore);
        }
      },
      child: Scaffold(
        appBar: CustomAppBar(
          title: 'Profile',
          showBackButton:
              false, // Disable back button for bottom navigation screen
          actions: [
            if (!_isEditing)
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () => setState(() => _isEditing = true),
              )
            else
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => setState(() => _isEditing = false),
              ),
          ],
        ),
        body: userModelAsync.when(
          data: (userModel) {
            // Debug logging to understand what data we're getting
            debugPrint('Profile screen user data: ${userModel?.toJson()}');

            if (userModel == null) {
              debugPrint(
                  'Profile screen: User model is null - creating fallback');

              // Check Firebase Auth state for fallback data
              final currentUser = FirebaseAuth.instance.currentUser;
              debugPrint(
                  'Profile screen: Firebase Auth user: ${currentUser?.uid}');
              debugPrint(
                  'Profile screen: Firebase Auth email: ${currentUser?.email}');
              debugPrint(
                  'Profile screen: Firebase Auth verified: ${currentUser?.emailVerified}');

              // Create fallback user model if Firebase Auth user exists
              if (currentUser != null) {
                debugPrint(
                    'Profile screen: Creating fallback user model from Firebase Auth');
                final fallbackUser =
                    ProfileFallbackData.createFallbackUserModel(currentUser);

                // Show the profile with fallback data and offline indicator
                return _buildProfileContent(fallbackUser, isDarkMode,
                    isOfflineMode: true);
              }

              // If no Firebase Auth user, show error state
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.person_off, size: 64, color: Colors.grey),
                    const SizedBox(height: 16),
                    const Text('User not found'),
                    const SizedBox(height: 8),
                    const Text('Please try logging out and back in',
                        style: TextStyle(color: Colors.grey)),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        // Force refresh the provider
                        ref.invalidate(currentUserModelProvider);
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            return _buildProfileContent(userModel, isDarkMode);
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) {
            // Debug logging for errors
            debugPrint('Profile screen error: $error');
            debugPrint('Profile screen stack trace: $stackTrace');

            // Try to provide fallback data even on error
            final currentUser = FirebaseAuth.instance.currentUser;
            if (currentUser != null) {
              debugPrint(
                  'Profile screen: Error occurred, but creating fallback user model');
              final fallbackUser =
                  ProfileFallbackData.createFallbackUserModel(currentUser);
              return _buildProfileContent(fallbackUser, isDarkMode,
                  isOfflineMode: true);
            }

            // If no Firebase Auth user, show error state
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  const Text('Error loading profile'),
                  const SizedBox(height: 8),
                  Text(
                    'Please check your connection and try again',
                    style: TextStyle(color: Colors.grey.shade600),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      // Refresh the provider
                      ref.invalidate(currentUserModelProvider);
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          },
        ),
        bottomNavigationBar: _isEditing
            ? Padding(
                padding: const EdgeInsets.all(16),
                child: CustomButton(
                  text: 'Save Changes',
                  onPressed: _saveProfile,
                  isLoading: _isLoading,
                ),
              )
            : null,
      ),
    );
  }

  Widget _buildOfflineIndicator() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingLg,
        vertical: AppTheme.spacingSm,
      ),
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: Colors.orange.withAlpha(51), // 20% opacity
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(color: Colors.orange.withAlpha(102)), // 40% opacity
      ),
      child: Row(
        children: [
          Icon(
            Icons.cloud_off,
            color: Colors.orange.shade700,
            size: 20,
          ),
          const SizedBox(width: AppTheme.spacingSm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Offline Mode',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeSm,
                    fontWeight: AppTheme.fontWeightSemibold,
                    color: Colors.orange.shade700,
                  ),
                ),
                Text(
                  'Using cached data. Some features may be limited.',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeXs,
                    color: Colors.orange.shade600,
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () {
              // Force refresh to try reconnecting
              ref.invalidate(currentUserModelProvider);
            },
            child: Text(
              'Retry',
              style: TextStyle(
                fontSize: AppTheme.fontSizeXs,
                color: Colors.orange.shade700,
                fontWeight: AppTheme.fontWeightSemibold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileContent(UserModel user, bool isDarkMode,
      {bool isOfflineMode = false}) {
    return RefreshIndicator(
      onRefresh: () async {
        debugPrint('Profile screen: Manual refresh triggered');
        try {
          // Only invalidate if we're not already loading
          final currentState = ref.read(currentUserModelProvider);
          if (!currentState.isLoading) {
            debugPrint('Profile screen: Invalidating provider for refresh');
            ref.invalidate(currentUserModelProvider);
          } else {
            debugPrint(
                'Profile screen: Provider already loading, skipping invalidation');
          }

          // Wait a bit for the provider to update
          await Future.delayed(const Duration(milliseconds: 500));

          // Load user data without additional provider calls
          await _loadUserData();
        } catch (e) {
          debugPrint('Profile screen: Error during refresh: $e');
        }
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            // Offline mode indicator
            if (isOfflineMode) _buildOfflineIndicator(),

            // Header section with greeting
            _buildHeaderSection(),

            // Profile card section
            _buildProfileHeader(user),
            const SizedBox(height: AppTheme.spacingXl),

            // Quick Actions grid
            _buildQuickActionsGrid(),
            const SizedBox(height: AppTheme.spacingXl),

            // Travel Statistics section
            _buildTravelStatistics(isOfflineMode: isOfflineMode),
            const SizedBox(height: AppTheme.spacingXl),

            // Achievements section
            _buildAchievements(isOfflineMode: isOfflineMode),
            const SizedBox(height: AppTheme.spacingXl),

            // Account Settings section
            _buildAccountSettings(),
            const SizedBox(height: AppTheme.spacingXl),

            // App Preferences section
            _buildAppPreferences(),
            const SizedBox(height: AppTheme.spacingXl),

            // Support & Legal section
            _buildSupportLegal(),
            const SizedBox(height: AppTheme.spacingXl),

            // Logout button
            _buildLogoutButton(),
            const SizedBox(height: AppTheme.spacingXl),

            // App info
            _buildAppInfo(),

            // Bottom padding for edit mode
            SizedBox(height: _isEditing ? 100 : AppTheme.spacingXl),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader(UserModel user) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Semantics(
      label: ProfileAccessibility.profileHeaderLabel,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
        child: Stack(
          children: [
            // Main profile card with React Native specifications and dark mode support
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingXl), // 32px padding
              decoration: BoxDecoration(
                color: isDarkMode
                    ? ProfileDarkTheme.cardBackground
                    : AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(
                    AppTheme.borderRadiusProfile), // 24px border radius
                boxShadow: isDarkMode
                    ? [
                        BoxShadow(
                          color: ProfileDarkTheme.shadowDark
                              .withAlpha(76), // 30% opacity
                          offset: const Offset(0, 6),
                          blurRadius: 16,
                          spreadRadius: 0,
                        )
                      ]
                    : AppTheme.shadowProfile, // React Native shadow specs
              ),
              child: Column(
                children: [
                  // Profile avatar with exact React Native specifications
                  Stack(
                    alignment: Alignment.bottomRight,
                    children: [
                      Container(
                        width: 80, // Exact React Native size
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white, // White border as per RN specs
                            width: 3,
                          ),
                          boxShadow: const [
                            BoxShadow(
                              color: AppTheme.shadowColor,
                              blurRadius: 8,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ClipOval(
                          child: Container(
                            color: AppTheme.backgroundSecondary,
                            child: Stack(
                              children: [
                                const Center(
                                  child: Icon(
                                    Icons.person,
                                    size: 40,
                                    color: AppTheme.textTertiaryColor,
                                  ),
                                ),
                                // Show actual image if available
                                if (_profileImageFile != null)
                                  Image.file(
                                    _profileImageFile!,
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                    height: double.infinity,
                                  )
                                else if (user.profilePicture != null)
                                  CachedNetworkImage(
                                    imageUrl: user.profilePicture!,
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                    height: double.infinity,
                                    errorWidget: (context, url, error) =>
                                        const SizedBox(),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      // Camera button with exact React Native specifications
                      if (_isEditing)
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: _pickImageWithFeedback,
                            child: Container(
                              width: 28, // Exact React Native size
                              height: 28,
                              decoration: BoxDecoration(
                                color: AppTheme.primaryColor, // Primary color
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white,
                                  width: 2, // 2px white border
                                ),
                                boxShadow: const [
                                  BoxShadow(
                                    color: AppTheme.shadowColor,
                                    blurRadius: 4,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.camera_alt,
                                size: 14,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: AppTheme.spacingLg), // 24px spacing

                  // User name with React Native typography and dark mode support
                  Text(
                    user.fullName.isNotEmpty ? user.fullName : 'Explorer',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeXxl, // 28px
                      fontWeight: AppTheme.fontWeightBold,
                      color: isDarkMode
                          ? ProfileDarkTheme.textPrimary
                          : AppTheme.textPrimaryColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppTheme.spacingSm), // 8px spacing

                  // User email with dark mode support
                  Text(
                    user.email,
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeMd, // 16px
                      color: isDarkMode
                          ? ProfileDarkTheme.textSecondary
                          : AppTheme.textSecondaryColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppTheme.spacingMd), // 16px spacing

                  // Premium membership badge with crown icon
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingMd,
                      vertical: AppTheme.spacingSm,
                    ),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [AppTheme.primaryColor, AppTheme.primaryLight],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius:
                          BorderRadius.circular(AppTheme.borderRadiusMedium),
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.primaryColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.workspace_premium,
                          size: 16,
                          color: Colors.white,
                        ),
                        SizedBox(width: AppTheme.spacingSm),
                        Text(
                          'Premium Explorer',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: AppTheme.fontSizeSm,
                            fontWeight: AppTheme.fontWeightSemibold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingLg), // 24px spacing

                  // Stats row with React Native specifications
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildStatItem("12", "Bookings"),
                      Container(
                        height: 40,
                        width: 1,
                        color: AppTheme.borderLight,
                      ),
                      _buildStatItem("4.8", "Rating"),
                      Container(
                        height: 40,
                        width: 1,
                        color: AppTheme.borderLight,
                      ),
                      _buildStatItem("1,250", "Points"),
                    ],
                  ),
                ],
              ),
            ),
            // Subtle gradient overlay matching React Native
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingXl),
              decoration: BoxDecoration(
                gradient: AppTheme.profileCardGradient,
                borderRadius:
                    BorderRadius.circular(AppTheme.borderRadiusProfile),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: AppTheme.fontSizeXl, // 20px
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.primaryColor, // Use primary color
          ),
        ),
        const SizedBox(height: AppTheme.spacingXs), // 4px
        Text(
          label,
          style: const TextStyle(
            fontSize: AppTheme.fontSizeSm, // 14px
            color: AppTheme.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  // React Native Profile Screen Sections

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingLg,
        vertical: AppTheme.spacingLg,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'Your Profile',
            style: TextStyle(
              fontSize: AppTheme.fontSizeXxl, // 28px
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          IconButton(
            onPressed: () {
              // Navigate to settings (placeholder)
            },
            icon: Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color: AppTheme.backgroundSecondary,
                borderRadius: BorderRadius.circular(22),
                boxShadow: AppTheme.shadowLight,
              ),
              child: const Icon(
                Icons.settings,
                color: AppTheme.textPrimaryColor,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsGrid() {
    // Check cache first for performance optimization
    const cacheKey = 'quick_actions_grid';
    final cachedWidget = _getCachedWidget(cacheKey);
    if (cachedWidget != null) {
      return cachedWidget;
    }

    final quickActions = [
      QuickAction(
        title: 'Edit Profile',
        description: 'Update your personal information',
        icon: Icons.edit,
        backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.15),
        onTap: () => setState(() => _isEditing = true),
      ),
      QuickAction(
        title: 'Travel Preferences',
        description: 'Customize your travel style',
        icon: Icons.tune,
        backgroundColor: AppTheme.secondaryColor.withValues(alpha: 0.15),
        onTap: () {
          // Navigate to preferences (placeholder)
        },
      ),
      QuickAction(
        title: 'Booking History',
        description: 'View your past bookings',
        icon: Icons.history,
        backgroundColor: AppTheme.accentColor.withValues(alpha: 0.15),
        onTap: () {
          // Navigate to booking history (placeholder)
        },
      ),
      QuickAction(
        title: 'Loyalty Points',
        description: 'Check your rewards',
        icon: Icons.stars,
        backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.15),
        onTap: () {
          // Navigate to loyalty dashboard
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const LoyaltyDashboardScreen(),
            ),
          );
        },
      ),
    ];

    final widget = Container(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Quick Actions',
            style: TextStyle(
              fontSize: AppTheme.fontSizeLg, // 18px
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          AnimatedBuilder(
            animation: _quickActionsAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, 20 * (1 - _quickActionsAnimation.value)),
                child: Opacity(
                  opacity: _quickActionsAnimation.value,
                  child: GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 2,
                    childAspectRatio:
                        1.35, // Increased from 1.2 to provide more height and prevent overflow
                    crossAxisSpacing: AppTheme.spacingMd,
                    mainAxisSpacing: AppTheme.spacingMd,
                    children: quickActions
                        .map((action) => _buildQuickActionCard(action))
                        .toList(),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );

    // Cache the widget for performance optimization
    _cacheWidget(cacheKey, widget);
    return widget;
  }

  Widget _buildQuickActionCard(QuickAction action) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return AnimatedScale(
      scale: 1.0,
      duration: ProfileAnimations.cardPress,
      child: GestureDetector(
        onTapDown: (_) {
          // Add subtle scale animation on press
        },
        onTapUp: (_) {
          // Reset scale and trigger action
          action.onTap();
        },
        onTapCancel: () {
          // Reset scale on cancel
        },
        child: Container(
          padding: const EdgeInsets.all(AppTheme.spacingLg), // 24px
          decoration: BoxDecoration(
            color: isDarkMode
                ? ProfileDarkTheme.cardBackgroundSecondary
                : action.backgroundColor,
            borderRadius:
                BorderRadius.circular(AppTheme.borderRadiusLarge), // 16px
            boxShadow: isDarkMode
                ? [
                    BoxShadow(
                      color: ProfileDarkTheme.shadowDark
                          .withAlpha(51), // 20% opacity
                      offset: const Offset(0, 2),
                      blurRadius: 8,
                      spreadRadius: 0,
                    )
                  ]
                : AppTheme.shadowLight,
            border: isDarkMode
                ? Border.all(color: ProfileDarkTheme.borderDark, width: 1)
                : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: AppTheme.shadowLight,
                ),
                child: Icon(
                  action.icon,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(height: AppTheme.spacingMd),
              Text(
                action.title,
                style: TextStyle(
                  fontSize: AppTheme.fontSizeMd, // 16px
                  fontWeight: AppTheme.fontWeightSemibold,
                  height:
                      1.2, // Explicit line height to control spacing precisely
                  color: isDarkMode
                      ? ProfileDarkTheme.textPrimary
                      : AppTheme.textPrimaryColor,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppTheme.spacingXs),
              Flexible(
                child: Text(
                  action.description,
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeSm, // 14px
                    height: 1.25, // Tight line height to prevent overflow
                    color: isDarkMode
                        ? ProfileDarkTheme.textSecondary
                        : AppTheme.textSecondaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTravelStatistics({bool isOfflineMode = false}) {
    // Use mock data when in offline mode, otherwise use real data
    final travelStats = isOfflineMode
        ? ProfileFallbackData.mockTravelStats
        : const [
            TravelStatistic(
              title: 'Countries Visited',
              value: '12',
              subtitle: 'of 195 countries',
              icon: Icons.public,
              iconColor: AppTheme.primaryColor,
              progress: 12 / 195,
              progressColor: AppTheme.primaryColor,
            ),
            TravelStatistic(
              title: 'Cities Explored',
              value: '28',
              subtitle: 'unique destinations',
              icon: Icons.location_city,
              iconColor: AppTheme.secondaryColor,
              progress: 0.7,
              progressColor: AppTheme.secondaryColor,
            ),
            TravelStatistic(
              title: 'Cultural Events',
              value: '45',
              subtitle: 'experiences attended',
              icon: Icons.festival,
              iconColor: AppTheme.accentColor,
              progress: 0.85,
              progressColor: AppTheme.accentColor,
            ),
            TravelStatistic(
              title: 'Local Guides',
              value: '18',
              subtitle: 'guides connected',
              icon: Icons.person_pin_circle,
              iconColor: AppTheme.primaryColor,
              progress: 0.6,
              progressColor: AppTheme.primaryColor,
            ),
          ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.trending_up,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              SizedBox(width: AppTheme.spacingSm),
              Text(
                'Travel Statistics',
                style: TextStyle(
                  fontSize: AppTheme.fontSizeLg, // 18px
                  fontWeight: AppTheme.fontWeightBold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSm),
          const Text(
            'Your journey at a glance',
            style: TextStyle(
              fontSize: AppTheme.fontSizeSm, // 14px
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLg),
          AnimatedBuilder(
            animation: _statisticsAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, 30 * (1 - _statisticsAnimation.value)),
                child: Opacity(
                  opacity: _statisticsAnimation.value,
                  child: GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 2,
                    childAspectRatio: 1.1,
                    crossAxisSpacing: AppTheme.spacingSm,
                    mainAxisSpacing: AppTheme.spacingMd,
                    children: travelStats
                        .map((stat) => _buildStatCard(stat))
                        .toList(),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(TravelStatistic stat) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd), // 16px
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge), // 16px
        boxShadow: AppTheme.shadowLight,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: stat.iconColor.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: Icon(
                  stat.icon,
                  color: stat.iconColor,
                  size: 18,
                ),
              ),
              const Spacer(),
              Text(
                stat.value,
                style: TextStyle(
                  fontSize: AppTheme.fontSizeLg, // 18px
                  fontWeight: AppTheme.fontWeightBold,
                  color: stat.iconColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Text(
            stat.title,
            style: const TextStyle(
              fontSize: AppTheme.fontSizeSm, // 14px
              fontWeight: AppTheme.fontWeightMedium,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXs),
          Text(
            stat.subtitle,
            style: const TextStyle(
              fontSize: AppTheme.fontSizeXs, // 12px
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          // Animated Progress bar
          AnimatedBuilder(
            animation: _statisticsAnimation,
            builder: (context, child) {
              return Container(
                height: 4,
                decoration: BoxDecoration(
                  color: AppTheme.backgroundSecondary,
                  borderRadius: BorderRadius.circular(2),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: stat.progress * _statisticsAnimation.value,
                  child: Container(
                    decoration: BoxDecoration(
                      color: stat.progressColor,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAchievements({bool isOfflineMode = false}) {
    // Use mock data when in offline mode, otherwise use real data
    final achievements = isOfflineMode
        ? ProfileFallbackData.mockAchievements
        : const [
            Achievement(
              title: 'Explorer',
              description: 'Visited 5 different countries',
              icon: Icons.explore,
              gradientColors: [AppTheme.primaryColor, AppTheme.primaryLight],
              unlocked: true,
            ),
            Achievement(
              title: 'Frequent Flyer',
              description: 'Completed 10 bookings',
              icon: Icons.flight,
              gradientColors: [AppTheme.secondaryColor, AppTheme.accentColor],
              unlocked: true,
            ),
            Achievement(
              title: 'Travel Guru',
              description: 'Earned 1000 loyalty points',
              icon: Icons.star,
              gradientColors: [Color(0xFFFFD700), Color(0xFFFFA500)],
              unlocked: false,
              progress: 0.75,
              progressText: '750/1000 points',
            ),
            Achievement(
              title: 'Social Traveler',
              description: 'Connected with 20 local guides',
              icon: Icons.people,
              gradientColors: [Color(0xFFFF6B6B), Color(0xFFFF8E8E)],
              unlocked: false,
              progress: 0.9,
              progressText: '18/20 guides',
            ),
          ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Row(
                children: [
                  Icon(
                    Icons.emoji_events,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                  SizedBox(width: AppTheme.spacingSm),
                  Text(
                    'Achievements',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeLg, // 18px
                      fontWeight: AppTheme.fontWeightBold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
              TextButton(
                onPressed: () {
                  // Navigate to all achievements
                },
                child: const Text(
                  'View All',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeSm,
                    color: AppTheme.primaryColor,
                    fontWeight: AppTheme.fontWeightMedium,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          AnimatedBuilder(
            animation: _achievementsAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, 40 * (1 - _achievementsAnimation.value)),
                child: Opacity(
                  opacity: _achievementsAnimation.value,
                  child: Column(
                    children: achievements
                        .map(
                            (achievement) => _buildAchievementCard(achievement))
                        .toList(),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementCard(Achievement achievement) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMd),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        gradient: achievement.unlocked
            ? LinearGradient(colors: achievement.gradientColors)
            : const LinearGradient(
                colors: [Color(0x33B0B0B0), Color(0x1AB0B0B0)]),
        boxShadow: AppTheme.shadowMedium,
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLg),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                color: Colors.white.withValues(alpha: 0.2),
              ),
              child: Icon(
                achievement.icon,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: AppTheme.spacingMd),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    achievement.title,
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeMd,
                      fontWeight: AppTheme.fontWeightBold,
                      color: achievement.unlocked
                          ? Colors.white
                          : AppTheme.textTertiaryColor,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingXs),
                  Text(
                    achievement.description,
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeSm,
                      color: achievement.unlocked
                          ? Colors.white.withValues(alpha: 0.9)
                          : AppTheme.textTertiaryColor,
                    ),
                  ),
                  if (!achievement.unlocked) ...[
                    const SizedBox(height: AppTheme.spacingSm),
                    _buildProgressBar(
                        achievement.progress, achievement.progressText),
                  ],
                ],
              ),
            ),
            if (achievement.unlocked)
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: Colors.white.withValues(alpha: 0.2),
                ),
                child: const Icon(Icons.check_circle,
                    color: Colors.white, size: 16),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar(double progress, String? progressText) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 6,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(3),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),
        ),
        if (progressText != null) ...[
          const SizedBox(height: AppTheme.spacingXs),
          Text(
            progressText,
            style: TextStyle(
              fontSize: AppTheme.fontSizeXs,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAccountSettings() {
    return _buildMenuSection(
      'Account Settings',
      Icons.person,
      [
        MenuItem(
          title: 'Personal Information',
          subtitle: 'Update your profile details',
          icon: Icons.edit,
          iconColor: AppTheme.primaryColor,
          onTap: () => setState(() => _isEditing = true),
        ),
        MenuItem(
          title: 'Security Settings',
          subtitle: 'Password and authentication',
          icon: Icons.security,
          iconColor: AppTheme.secondaryColor,
          onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => const SecuritySettingsScreen())),
        ),
        MenuItem(
          title: 'Privacy Settings',
          subtitle: 'Control your data and visibility',
          icon: Icons.privacy_tip,
          iconColor: AppTheme.accentColor,
          onTap: () {/* Navigate to privacy settings */},
        ),
      ],
    );
  }

  Widget _buildAppPreferences() {
    return _buildMenuSection(
      'App Preferences',
      Icons.tune,
      [
        MenuItem(
          title: 'Notifications',
          subtitle: 'Push notifications and alerts',
          icon: Icons.notifications,
          iconColor: AppTheme.primaryColor,
          trailing: Switch(
            value: true,
            onChanged: (value) {/* Handle notification toggle */},
            activeColor: AppTheme.primaryColor,
          ),
        ),
        MenuItem(
          title: 'Location Services',
          subtitle: 'GPS and location tracking',
          icon: Icons.location_on,
          iconColor: AppTheme.secondaryColor,
          trailing: Switch(
            value: true,
            onChanged: (value) {/* Handle location toggle */},
            activeColor: AppTheme.primaryColor,
          ),
        ),
        MenuItem(
          title: 'Dark Mode',
          subtitle: 'App appearance theme',
          icon: Icons.dark_mode,
          iconColor: AppTheme.accentColor,
          trailing: Switch(
            value: false,
            onChanged: (value) {/* Handle dark mode toggle */},
            activeColor: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildSupportLegal() {
    return _buildMenuSection(
      'Support & Legal',
      Icons.help,
      [
        MenuItem(
          title: 'Help Center',
          subtitle: 'FAQs and support articles',
          icon: Icons.help_center,
          iconColor: AppTheme.primaryColor,
          onTap: () {/* Navigate to help center */},
        ),
        MenuItem(
          title: 'Contact Support',
          subtitle: 'Get help from our team',
          icon: Icons.support_agent,
          iconColor: AppTheme.secondaryColor,
          onTap: () {/* Navigate to contact support */},
        ),
        MenuItem(
          title: 'Terms & Privacy',
          subtitle: 'Legal information',
          icon: Icons.gavel,
          iconColor: AppTheme.accentColor,
          onTap: () {/* Navigate to terms and privacy */},
        ),
      ],
    );
  }

  Widget _buildMenuSection(
      String title, IconData titleIcon, List<MenuItem> items) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(titleIcon, color: AppTheme.primaryColor, size: 20),
              const SizedBox(width: AppTheme.spacingSm),
              Text(
                title,
                style: const TextStyle(
                  fontSize: AppTheme.fontSizeLg,
                  fontWeight: AppTheme.fontWeightBold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              boxShadow: AppTheme.shadowLight,
            ),
            child: Column(
              children: items.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                final isLast = index == items.length - 1;

                return _buildMenuItem(item, isLast);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(MenuItem item, bool isLast) {
    return InkWell(
      onTap: item.onTap,
      borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingMd,
          vertical: AppTheme.spacingMd,
        ),
        decoration: BoxDecoration(
          border: isLast
              ? null
              : const Border(
                  bottom: BorderSide(color: AppTheme.borderLight, width: 1),
                ),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: item.iconColor.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(item.icon, color: item.iconColor, size: 20),
            ),
            const SizedBox(width: AppTheme.spacingMd),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.title,
                    style: const TextStyle(
                      fontSize: AppTheme.fontSizeMd,
                      fontWeight: AppTheme.fontWeightMedium,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  if (item.subtitle != null) ...[
                    const SizedBox(height: AppTheme.spacingXs),
                    Text(
                      item.subtitle!,
                      style: const TextStyle(
                        fontSize: AppTheme.fontSizeSm,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (item.trailing != null)
              item.trailing!
            else
              const Icon(Icons.chevron_right,
                  color: AppTheme.textTertiaryColor, size: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () => _showLogoutDialog(),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.errorColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            ),
            elevation: 2,
          ),
          child: const Text(
            'Logout',
            style: TextStyle(
              fontSize: AppTheme.fontSizeMd,
              fontWeight: AppTheme.fontWeightSemibold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppInfo() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: const Column(
        children: [
          Text(
            'CultureConnect',
            style: TextStyle(
              fontSize: AppTheme.fontSizeSm,
              fontWeight: AppTheme.fontWeightMedium,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: AppTheme.spacingXs),
          Text(
            'Version 1.0.0',
            style: TextStyle(
              fontSize: AppTheme.fontSizeXs,
              color: AppTheme.textTertiaryColor,
            ),
          ),
          SizedBox(height: AppTheme.spacingXs),
          Text(
            'The Soul of Travel, Guided by AI',
            style: TextStyle(
              fontSize: AppTheme.fontSizeXs,
              color: AppTheme.textTertiaryColor,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _signOut();
              },
              style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorColor),
              child:
                  const Text('Logout', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }
}
