import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/booking_model.dart' as model;
import 'package:culture_connect/models/experience.dart';
import 'notification_service.dart';
import 'cache_service.dart';

// Type alias to make the code more readable
typedef BookingModel = model.BookingModel;

/// Provider for the booking service
final bookingServiceProvider = Provider<BookingService>((ref) {
  return BookingService();
});

/// Service for managing bookings
class BookingService {
  // Singleton instance
  static final BookingService _instance = BookingService._internal();
  factory BookingService() => _instance;
  BookingService._internal();

  // Development flag to enable/disable mock data
  // Set to true for testing the redesigned booking screen with comprehensive data
  // Set to false for production or when using real API data
  static const bool _useMockData = true;

  // In-memory storage for bookings (replace with API calls in production)
  final List<Booking> _bookings = [];

  /// Check if a date is available for booking
  Future<bool> isDateAvailable(Experience experience, DateTime date) async {
    // TODO: Implement actual availability check with backend
    // For now, return true for demo purposes
    return true;
  }

  /// Get available time slots for a given date
  Future<List<TimeSlot>> getAvailableTimeSlots(
    Experience experience,
    DateTime date,
  ) async {
    // TODO: Implement actual time slot fetching with backend
    // For now, return mock data
    return [
      TimeSlot(
        startTime: DateTime(date.year, date.month, date.day, 9, 0),
        endTime: DateTime(date.year, date.month, date.day, 12, 0),
        isAvailable: true,
      ),
      TimeSlot(
        startTime: DateTime(date.year, date.month, date.day, 13, 0),
        endTime: DateTime(date.year, date.month, date.day, 16, 0),
        isAvailable: true,
      ),
      TimeSlot(
        startTime: DateTime(date.year, date.month, date.day, 17, 0),
        endTime: DateTime(date.year, date.month, date.day, 20, 0),
        isAvailable: false,
      ),
    ];
  }

  /// Calculate the total price for a booking
  double calculateTotalPrice(
    Experience experience,
    int participantCount,
    DateTime date,
    TimeSlot timeSlot,
  ) {
    return experience.price * participantCount;
  }

  /// Create a new booking
  Future<Booking> createBooking({
    required Experience experience,
    required DateTime date,
    required TimeSlot timeSlot,
    required int participantCount,
    required String specialRequirements,
  }) async {
    // TODO: Implement actual booking creation with backend
    // For now, create a booking in memory
    final booking = Booking(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      experienceId: experience.id,
      date: date,
      timeSlot: timeSlot,
      participantCount: participantCount,
      totalAmount: calculateTotalPrice(
        experience,
        participantCount,
        date,
        timeSlot,
      ),
      status: BookingStatus.pending,
      specialRequirements: specialRequirements,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Add to in-memory storage
    _bookings.add(booking);

    return booking;
  }

  /// Update booking status
  Future<void> updateBookingStatus(
    String bookingId,
    BookingStatus status,
    String? transactionId,
  ) async {
    // TODO: Implement actual backend call
    // For now, update in-memory booking
    final index = _bookings.indexWhere((b) => b.id == bookingId);
    if (index != -1) {
      final booking = _bookings[index];
      _bookings[index] = booking.copyWith(
        status: status,
        updatedAt: DateTime.now(),
      );
    }

    // In a real app, this would be an API call:
    // await _apiService.updateBookingStatus(bookingId, status, transactionId);

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// Get all bookings for the current user
  Future<List<Booking>> getUserBookings() async {
    // Get cache service
    final cacheService = CacheService();

    try {
      // Check if we're online (in a real app, use connectivity package)
      bool isOnline =
          DateTime.now().second % 2 == 0; // Randomly simulate online/offline

      if (isOnline) {
        // TODO: Implement actual backend call
        // In a real app, this would be an API call:
        // final bookings = await _apiService.getUserBookings();

        // Simulate network delay
        await Future.delayed(const Duration(seconds: 1));

        // Sort by date (most recent first)
        final sortedBookings = List<Booking>.from(_bookings);
        sortedBookings.sort((a, b) => b.date.compareTo(a.date));

        // If no bookings exist and mock data is enabled, create comprehensive mock data for testing
        if (sortedBookings.isEmpty && _useMockData) {
          final now = DateTime.now();

          // Add comprehensive mock bookings for testing all scenarios
          _bookings.addAll([
            // CONFIRMED BOOKINGS (Upcoming)
            Booking(
              id: 'BK001',
              experienceId: 'Cultural Walking Tour of Historic Tokyo',
              date: DateTime(now.year, now.month, now.day + 3),
              timeSlot: TimeSlot(
                startTime: DateTime(now.year, now.month, now.day + 3, 9, 0),
                endTime: DateTime(now.year, now.month, now.day + 3, 12, 0),
              ),
              participantCount: 2,
              totalAmount: 85.0,
              status: BookingStatus.confirmed,
              specialRequirements:
                  'Please provide vegetarian lunch options and ensure wheelchair accessibility for one participant.',
              createdAt: DateTime.now().subtract(const Duration(days: 2)),
              updatedAt: DateTime.now().subtract(const Duration(days: 1)),
            ),

            Booking(
              id: 'BK002',
              experienceId: 'Sunset Photography Workshop',
              date: DateTime(now.year, now.month, now.day + 7),
              timeSlot: TimeSlot(
                startTime: DateTime(now.year, now.month, now.day + 7, 17, 30),
                endTime: DateTime(now.year, now.month, now.day + 7, 20, 0),
              ),
              participantCount: 1,
              totalAmount: 125.0,
              status: BookingStatus.confirmed,
              specialRequirements: '',
              createdAt: DateTime.now().subtract(const Duration(days: 5)),
              updatedAt: DateTime.now().subtract(const Duration(days: 5)),
            ),

            Booking(
              id: 'BK003',
              experienceId: 'Traditional Cooking Class & Market Tour',
              date: DateTime(now.year, now.month, now.day + 14),
              timeSlot: TimeSlot(
                startTime: DateTime(now.year, now.month, now.day + 14, 10, 0),
                endTime: DateTime(now.year, now.month, now.day + 14, 15, 0),
              ),
              participantCount: 4,
              totalAmount: 320.0,
              status: BookingStatus.confirmed,
              specialRequirements:
                  'Two participants have nut allergies. Please ensure all ingredients are nut-free.',
              createdAt: DateTime.now().subtract(const Duration(days: 8)),
              updatedAt: DateTime.now().subtract(const Duration(days: 7)),
            ),

            // PENDING BOOKINGS
            Booking(
              id: 'BK004',
              experienceId: 'Private Art Gallery Tour with Curator',
              date: DateTime(now.year, now.month, now.day + 5),
              timeSlot: TimeSlot(
                startTime: DateTime(now.year, now.month, now.day + 5, 14, 0),
                endTime: DateTime(now.year, now.month, now.day + 5, 16, 30),
              ),
              participantCount: 3,
              totalAmount: 180.0,
              status: BookingStatus.pending,
              specialRequirements:
                  'Interested in contemporary art focus, particularly local emerging artists.',
              createdAt: DateTime.now().subtract(const Duration(hours: 12)),
              updatedAt: DateTime.now().subtract(const Duration(hours: 12)),
            ),

            Booking(
              id: 'BK005',
              experienceId: 'Mountain Hiking Adventure',
              date: DateTime(now.year, now.month, now.day + 10),
              timeSlot: TimeSlot(
                startTime: DateTime(now.year, now.month, now.day + 10, 7, 0),
                endTime: DateTime(now.year, now.month, now.day + 10, 17, 0),
              ),
              participantCount: 6,
              totalAmount: 450.0,
              status: BookingStatus.pending,
              specialRequirements: '',
              createdAt: DateTime.now().subtract(const Duration(hours: 3)),
              updatedAt: DateTime.now().subtract(const Duration(hours: 3)),
            ),

            // COMPLETED BOOKINGS (Past)
            Booking(
              id: 'BK006',
              experienceId: 'Street Food Discovery Tour',
              date: DateTime(now.year, now.month, now.day - 5),
              timeSlot: TimeSlot(
                startTime: DateTime(now.year, now.month, now.day - 5, 18, 0),
                endTime: DateTime(now.year, now.month, now.day - 5, 21, 0),
              ),
              participantCount: 2,
              totalAmount: 95.0,
              status: BookingStatus.completed,
              specialRequirements:
                  'One participant is vegetarian, please include vegetarian street food options.',
              createdAt: DateTime.now().subtract(const Duration(days: 12)),
              updatedAt: DateTime.now().subtract(const Duration(days: 4)),
            ),

            Booking(
              id: 'BK007',
              experienceId: 'Traditional Tea Ceremony Experience',
              date: DateTime(now.year, now.month, now.day - 15),
              timeSlot: TimeSlot(
                startTime: DateTime(now.year, now.month, now.day - 15, 15, 0),
                endTime: DateTime(now.year, now.month, now.day - 15, 17, 0),
              ),
              participantCount: 1,
              totalAmount: 65.0,
              status: BookingStatus.completed,
              specialRequirements: '',
              createdAt: DateTime.now().subtract(const Duration(days: 20)),
              updatedAt: DateTime.now().subtract(const Duration(days: 14)),
            ),

            // CANCELLED BOOKINGS
            Booking(
              id: 'BK008',
              experienceId: 'Nightlife & Bar Hopping Tour',
              date: DateTime(now.year, now.month, now.day - 3),
              timeSlot: TimeSlot(
                startTime: DateTime(now.year, now.month, now.day - 3, 20, 0),
                endTime: DateTime(now.year, now.month, now.day - 3, 23, 30),
              ),
              participantCount: 4,
              totalAmount: 160.0,
              status: BookingStatus.cancelled,
              specialRequirements:
                  'Group prefers craft beer venues over cocktail bars.',
              createdAt: DateTime.now().subtract(const Duration(days: 10)),
              updatedAt: DateTime.now().subtract(const Duration(days: 2)),
            ),

            // REFUNDED BOOKINGS
            Booking(
              id: 'BK009',
              experienceId: 'Luxury Spa & Wellness Retreat',
              date: DateTime(now.year, now.month, now.day - 8),
              timeSlot: TimeSlot(
                startTime: DateTime(now.year, now.month, now.day - 8, 10, 0),
                endTime: DateTime(now.year, now.month, now.day - 8, 16, 0),
              ),
              participantCount: 2,
              totalAmount: 380.0,
              status: BookingStatus.refunded,
              specialRequirements:
                  'Both participants prefer Swedish massage over deep tissue. Please ensure quiet environment.',
              createdAt: DateTime.now().subtract(const Duration(days: 25)),
              updatedAt: DateTime.now().subtract(const Duration(days: 7)),
            ),

            // MORE UPCOMING CONFIRMED BOOKINGS (Different price ranges)
            Booking(
              id: 'BK010',
              experienceId: 'Budget Backpacker City Walk',
              date: DateTime(now.year, now.month, now.day + 1),
              timeSlot: TimeSlot(
                startTime: DateTime(now.year, now.month, now.day + 1, 14, 0),
                endTime: DateTime(now.year, now.month, now.day + 1, 16, 0),
              ),
              participantCount: 1,
              totalAmount: 25.0,
              status: BookingStatus.confirmed,
              specialRequirements: '',
              createdAt: DateTime.now().subtract(const Duration(hours: 18)),
              updatedAt: DateTime.now().subtract(const Duration(hours: 18)),
            ),

            Booking(
              id: 'BK011',
              experienceId: 'Premium Wine Tasting & Vineyard Tour',
              date: DateTime(now.year, now.month, now.day + 21),
              timeSlot: TimeSlot(
                startTime: DateTime(now.year, now.month, now.day + 21, 11, 0),
                endTime: DateTime(now.year, now.month, now.day + 21, 17, 0),
              ),
              participantCount: 8,
              totalAmount: 720.0,
              status: BookingStatus.confirmed,
              specialRequirements:
                  'Group includes sommelier-level wine enthusiasts. Please arrange advanced tasting with rare vintages if possible.',
              createdAt: DateTime.now().subtract(const Duration(days: 14)),
              updatedAt: DateTime.now().subtract(const Duration(days: 13)),
            ),

            // EDGE CASE: Very long experience title
            Booking(
              id: 'BK012',
              experienceId:
                  'Comprehensive Cultural Immersion Experience Including Traditional Crafts Workshop, Historical Site Visits, Local Family Dinner, and Storytelling Session',
              date: DateTime(now.year, now.month, now.day + 30),
              timeSlot: TimeSlot(
                startTime: DateTime(now.year, now.month, now.day + 30, 9, 0),
                endTime: DateTime(now.year, now.month, now.day + 30, 18, 0),
              ),
              participantCount: 5,
              totalAmount: 495.0,
              status: BookingStatus.confirmed,
              specialRequirements:
                  'This is a very long special requirements text to test how the UI handles extensive user input. The group has specific dietary restrictions including gluten-free, dairy-free, and vegan options needed. Additionally, they would like to focus on traditional textile crafts rather than pottery, and prefer historical sites from the Edo period specifically. Please arrange for English-speaking guides throughout the entire experience.',
              createdAt: DateTime.now().subtract(const Duration(days: 3)),
              updatedAt: DateTime.now().subtract(const Duration(days: 2)),
            ),
          ]);

          // Sort again after adding mock data
          sortedBookings.clear();
          sortedBookings.addAll(_bookings);
          sortedBookings.sort((a, b) => b.date.compareTo(a.date));
        }

        // Cache the bookings for offline access
        final List<Map<String, dynamic>> bookingsJson =
            sortedBookings.map((booking) {
          return {
            'id': booking.id,
            'experienceId': booking.experienceId,
            'date': booking.date.toIso8601String(),
            'timeSlot': {
              'startTime': booking.timeSlot.startTime.toIso8601String(),
              'endTime': booking.timeSlot.endTime.toIso8601String(),
            },
            'participantCount': booking.participantCount,
            'totalAmount': booking.totalAmount,
            'status': booking.status.toString(),
            'specialRequirements': booking.specialRequirements,
            'createdAt': booking.createdAt.toIso8601String(),
            'updatedAt': booking.updatedAt.toIso8601String(),
          };
        }).toList();

        // Save to cache
        await cacheService.saveData(cacheService.bookingsKey, bookingsJson);

        return sortedBookings;
      } else {
        // We're offline, try to get data from cache
        debugPrint('Offline mode: Loading bookings from cache');
        return await _getBookingsFromCache();
      }
    } catch (e) {
      // Error occurred, try to get data from cache
      debugPrint('Error loading bookings: $e. Trying to load from cache');
      return await _getBookingsFromCache();
    }
  }

  /// Get bookings from cache
  Future<List<Booking>> _getBookingsFromCache() async {
    final cacheService = CacheService();

    // Get cached data
    final cachedData = await cacheService.getData(cacheService.bookingsKey);

    if (cachedData != null) {
      try {
        // Convert cached data to bookings
        final List<Booking> bookings = (cachedData as List).map((item) {
          final Map<String, dynamic> bookingMap =
              Map<String, dynamic>.from(item);

          // Parse time slot
          final timeSlotMap = Map<String, dynamic>.from(bookingMap['timeSlot']);
          final timeSlot = TimeSlot(
            startTime: DateTime.parse(timeSlotMap['startTime']),
            endTime: DateTime.parse(timeSlotMap['endTime']),
          );

          // Parse status
          BookingStatus status;
          final statusStr = bookingMap['status'] as String;
          if (statusStr.contains('pending')) {
            status = BookingStatus.pending;
          } else if (statusStr.contains('confirmed')) {
            status = BookingStatus.confirmed;
          } else if (statusStr.contains('cancelled')) {
            status = BookingStatus.cancelled;
          } else if (statusStr.contains('completed')) {
            status = BookingStatus.completed;
          } else if (statusStr.contains('refunded')) {
            status = BookingStatus.refunded;
          } else {
            status = BookingStatus.pending; // Default
          }

          return Booking(
            id: bookingMap['id'],
            experienceId: bookingMap['experienceId'],
            date: DateTime.parse(bookingMap['date']),
            timeSlot: timeSlot,
            participantCount: bookingMap['participantCount'],
            totalAmount: bookingMap['totalAmount'],
            status: status,
            specialRequirements: bookingMap['specialRequirements'],
            createdAt: DateTime.parse(bookingMap['createdAt']),
            updatedAt: DateTime.parse(bookingMap['updatedAt']),
          );
        }).toList();

        return bookings;
      } catch (e) {
        debugPrint('Error parsing cached bookings: $e');
        return [];
      }
    }

    return [];
  }

  /// Get upcoming bookings for the current user
  Future<List<model.BookingModel>> getUpcomingBookings() async {
    // In a real app, this would call an API to get upcoming bookings
    // For now, return an empty list
    return [];
  }

  /// Cancel a booking
  Future<void> cancelBooking(String bookingId) async {
    // TODO: Implement actual backend call
    // For now, update in-memory booking
    final index = _bookings.indexWhere((b) => b.id == bookingId);
    if (index != -1) {
      final booking = _bookings[index];
      _bookings[index] = booking.copyWith(
        status: BookingStatus.cancelled,
        updatedAt: DateTime.now(),
      );
    }

    // In a real app, this would be an API call:
    // await _apiService.cancelBooking(bookingId);

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));
  }

  /// Request a refund for a booking
  Future<bool> requestRefund(String bookingId, String reason) async {
    // TODO: Implement actual backend call
    // For now, update in-memory booking
    final index = _bookings.indexWhere((b) => b.id == bookingId);
    if (index != -1) {
      final booking = _bookings[index];
      _bookings[index] = booking.copyWith(
        status: BookingStatus.refunded,
        updatedAt: DateTime.now(),
      );
    }

    // In a real app, this would be an API call:
    // return await _apiService.requestRefund(bookingId, reason);

    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Simulate success
    return true;
  }

  /// Add a booking to the device calendar
  Future<bool> addBookingToCalendar(Booking booking) async {
    try {
      // In a real implementation, we would use a calendar integration package
      // like add_2_calendar to add the event to the device calendar

      // For now, simulate success after a delay
      await Future.delayed(const Duration(milliseconds: 800));
      return true;
    } catch (e) {
      // Log error
      debugPrint('Error adding to calendar: $e');
      return false;
    }
  }

  /// Set up booking reminders
  Future<void> setupBookingReminders(Booking booking) async {
    try {
      // Get the notification service
      final notificationService = NotificationService();

      // Request notification permissions
      final hasPermission = await notificationService.requestPermissions();
      if (!hasPermission) {
        debugPrint('Notification permissions denied');
        return;
      }

      // Set up reminders (24h and 1h before)
      await notificationService.setupBookingReminders(booking);

      debugPrint('Booking reminders set up successfully');
    } catch (e) {
      debugPrint('Error setting up booking reminders: $e');
    }
  }

  /// Get all bookings for the current guide
  Future<List<model.BookingModel>> getGuideBookings() async {
    // In a real app, this would call an API to get guide bookings
    // For now, return an empty list
    return [];
  }

  /// Get a booking by ID
  Future<model.BookingModel?> getBookingById(String bookingId) async {
    // In a real app, this would call an API to get a booking by ID
    // For now, return null
    return null;
  }

  /// Approve a booking
  Future<model.BookingModel> approveBooking(String bookingId) async {
    // In a real app, this would call an API to approve a booking
    // For now, return a mock booking
    return model.BookingModel(
      id: bookingId,
      guideId: 'guide-123',
      customer: model.BookingCustomer(
        id: 'customer-123',
        name: 'John Doe',
        email: '<EMAIL>',
      ),
      experience: model.BookingExperience(
        id: 'exp-123',
        title: 'City Tour',
        description: 'A tour of the city',
        category: 'Tour',
        price: 100.0,
        currency: 'USD',
        durationMinutes: 120,
      ),
      bookingDate: DateTime.now(),
      experienceDate: DateTime.now().add(const Duration(days: 7)),
      startTime: '10:00 AM',
      participantCount: 2,
      status: model.BookingStatus.approved,
      payment: model.PaymentInfo(
        id: 'payment-123',
        status: model.PaymentStatus.paid,
        amount: 100.0,
        currency: 'USD',
      ),
      isReviewed: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Reject a booking
  Future<model.BookingModel> rejectBooking(
      String bookingId, String rejectionReason) async {
    // In a real app, this would call an API to reject a booking
    // For now, return a mock booking
    return model.BookingModel(
      id: bookingId,
      guideId: 'guide-123',
      customer: model.BookingCustomer(
        id: 'customer-123',
        name: 'John Doe',
        email: '<EMAIL>',
      ),
      experience: model.BookingExperience(
        id: 'exp-123',
        title: 'City Tour',
        description: 'A tour of the city',
        category: 'Tour',
        price: 100.0,
        currency: 'USD',
        durationMinutes: 120,
      ),
      bookingDate: DateTime.now(),
      experienceDate: DateTime.now().add(const Duration(days: 7)),
      startTime: '10:00 AM',
      participantCount: 2,
      status: model.BookingStatus.rejected,
      rejectionReason: rejectionReason,
      payment: model.PaymentInfo(
        id: 'payment-123',
        status: model.PaymentStatus.refunded,
        amount: 100.0,
        currency: 'USD',
      ),
      isReviewed: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Complete a booking
  Future<model.BookingModel> completeBooking(String bookingId) async {
    // In a real app, this would call an API to complete a booking
    // For now, return a mock booking
    return model.BookingModel(
      id: bookingId,
      guideId: 'guide-123',
      customer: model.BookingCustomer(
        id: 'customer-123',
        name: 'John Doe',
        email: '<EMAIL>',
      ),
      experience: model.BookingExperience(
        id: 'exp-123',
        title: 'City Tour',
        description: 'A tour of the city',
        category: 'Tour',
        price: 100.0,
        currency: 'USD',
        durationMinutes: 120,
      ),
      bookingDate: DateTime.now(),
      experienceDate: DateTime.now().subtract(const Duration(days: 1)),
      startTime: '10:00 AM',
      participantCount: 2,
      status: model.BookingStatus.completed,
      payment: model.PaymentInfo(
        id: 'payment-123',
        status: model.PaymentStatus.paid,
        amount: 100.0,
        currency: 'USD',
      ),
      isReviewed: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Mark a booking as no-show
  Future<model.BookingModel> markAsNoShow(String bookingId) async {
    // In a real app, this would call an API to mark a booking as no-show
    // For now, return a mock booking
    return model.BookingModel(
      id: bookingId,
      guideId: 'guide-123',
      customer: model.BookingCustomer(
        id: 'customer-123',
        name: 'John Doe',
        email: '<EMAIL>',
      ),
      experience: model.BookingExperience(
        id: 'exp-123',
        title: 'City Tour',
        description: 'A tour of the city',
        category: 'Tour',
        price: 100.0,
        currency: 'USD',
        durationMinutes: 120,
      ),
      bookingDate: DateTime.now(),
      experienceDate: DateTime.now().subtract(const Duration(days: 1)),
      startTime: '10:00 AM',
      participantCount: 2,
      status: model.BookingStatus.noShow,
      payment: model.PaymentInfo(
        id: 'payment-123',
        status: model.PaymentStatus.paid,
        amount: 100.0,
        currency: 'USD',
      ),
      isReviewed: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Process a refund for a booking
  Future<model.BookingModel> processRefund(
    String bookingId,
    double refundAmount,
    String refundReason,
  ) async {
    // In a real app, this would call an API to process a refund
    // For now, return a mock booking
    return model.BookingModel(
      id: bookingId,
      guideId: 'guide-123',
      customer: model.BookingCustomer(
        id: 'customer-123',
        name: 'John Doe',
        email: '<EMAIL>',
      ),
      experience: model.BookingExperience(
        id: 'exp-123',
        title: 'City Tour',
        description: 'A tour of the city',
        category: 'Tour',
        price: 100.0,
        currency: 'USD',
        durationMinutes: 120,
      ),
      bookingDate: DateTime.now(),
      experienceDate: DateTime.now().subtract(const Duration(days: 1)),
      startTime: '10:00 AM',
      participantCount: 2,
      status: model.BookingStatus.cancelled,
      cancellationReason: refundReason,
      payment: model.PaymentInfo(
        id: 'payment-123',
        status: model.PaymentStatus.refunded,
        amount: 100.0,
        currency: 'USD',
        refundAmount: refundAmount,
        refundDate: DateTime.now(),
        refundReason: refundReason,
      ),
      isReviewed: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Send a notification to a customer
  Future<void> sendCustomerNotification(
      String bookingId, String message) async {
    // In a real app, this would call an API to send a notification
    // For now, just log the message
    debugPrint(
        'Sending notification to customer for booking $bookingId: $message');
  }

  /// Get past bookings for the current guide
  Future<List<model.BookingModel>> getPastBookings() async {
    // In a real app, this would call an API to get past bookings
    // For now, return an empty list
    return [];
  }

  /// Get pending bookings for the current guide
  Future<List<model.BookingModel>> getPendingBookings() async {
    // In a real app, this would call an API to get pending bookings
    // For now, return an empty list
    return [];
  }
}
